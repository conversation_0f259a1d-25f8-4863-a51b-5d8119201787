using RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions;

public sealed class MappingConfig : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        config.NewConfig<SearchCriteriaNLPModel, SearchCriteriaViewModel>()
            .Map(dest => dest.listingCategoryId, src => (int)src.ListingCategory)
            .Map(dest => dest.buildingPeriods, src => src.BuildingPeriods)
            .Map(dest => dest.ownershipType, src => src.OwnershipType)
            .Map(dest => dest.amenities, src => src.Amenities)
            .Map(dest => dest.attendedLobby, src => src.AttendedLobby)
            .Map(dest => dest.pricemin, src => src.PriceMin)
            .Map(dest => dest.pricemax, src => src.PriceMax)
            .Map(dest => dest.rooms, src => src.RoomsMin)
            .Map(dest => dest.maxrooms, src => src.RoomsMax)
            .Map(dest => dest.bathrooms, src => src.BathroomsMin)
            .Map(dest => dest.maxbathrooms, src => src.BathroomsMax)
            .Map(dest => dest.bedrooms, src => src.BedroomsMin)
            .Map(dest => dest.maxbedrooms, src => src.BedroomsMax)
            .Map(dest => dest.minSqft, src => src.SqFtMin)
            .Map(dest => dest.maxSqft, src => src.SqFtMax)
            .Map(dest => dest.areaOrNeighborhood,
                src => src.Neighborhoods == null || src.Neighborhoods.Length == 0
                ? null : string.Join(",", src.Neighborhoods.Select(x => x.Id)))
            .Map(dest => dest.tempAreaOrNeighborhoodIds,
                src => src.Neighborhoods == null || src.Neighborhoods.Length == 0
                ? null : string.Join("$$", src.Neighborhoods.Select(x => x.Id)))
            .Map(dest => dest.quickSearchInfo,
                src => src.Neighborhoods == null || src.Neighborhoods.Length == 0
                ? null : string.Join("$$", src.Neighborhoods.Select(x => $"Neighborhood: {x.Name}")))
            .Map(dest => dest.listingCategorystatus,
                src => src.ListingStatus == null || src.ListingStatus.Length == 0
                ? null : string.Join(",", GetListingStatuses(src.ListingStatus, src.ListingCategory)))
            // defaults
            .Map(dest => dest.roomsPlus, src => true)
            .Map(dest => dest.bedroomsPlus, src => true)
            .Map(dest => dest.bathroomsPlus, src => true);
    }

    static int[] GetListingStatuses(ListingStatus[] listingStatuses, ListingCategoryType listingCategory) =>
        listingStatuses?.SelectMany(x => (IEnumerable<int>)(x switch
        {
            ListingStatus.Active => listingCategory switch
            {
                ListingCategoryType.Sales => [163, 3593, 3594, 3688, 3595, 3687, 3596, 3689, 3848],
                ListingCategoryType.Rentals => [163, 3593, 3599, 3600, 3601, 3694, 3692, 3693],
                _ => throw new ArgumentOutOfRangeException(nameof(listingCategory))
            },
            ListingStatus.ContractSigned => [3312, 3323, 3306, 3277, 3280],
            ListingStatus.LeaseSigned => [219, 3695, 3597],
            ListingStatus.Sold => [156, 3598],
            ListingStatus.Rented => [220, 3602],
            ListingStatus.Closed => listingCategory switch
            {
                ListingCategoryType.Sales => [156, 3598],
                ListingCategoryType.Rentals => [220, 3602],
                _ => throw new ArgumentOutOfRangeException(nameof(listingCategory))
            },
            ListingStatus.OffMarket => [220, 3602],
            _ => throw new ArgumentOutOfRangeException(nameof(x))
        })).ToArray() ?? [];
}
